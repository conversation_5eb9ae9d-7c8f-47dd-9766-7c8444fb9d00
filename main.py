# الملف الرئيسي لوكيل أخبار الألعاب
import asyncio
import signal
import sys
import os
import re
import hashlib
import random
import sqlite3
import webbrowser
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import traceback

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.logger import logger
from modules.database import db
from modules.content_scraper import ContentScraper
from modules.youtube_analyzer import YouTubeAnalyzer
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer
from modules.web_approval_system import web_approval_system
from modules.content_generator import ContentGenerator
from modules.publisher import PublisherManager
from modules.analytics import analytics
from modules.user_engagement import engagement_engine
from modules.visitor_analytics import visitor_analytics
from modules.intelligent_cms import intelligent_cms
from modules.ai_personality import ai_personality
from modules.article_performance_analyzer import article_performance_analyzer
from modules.content_optimizer import content_optimizer
from modules.advanced_seo_system import advanced_seo_system
from modules.api_integrations import api_manager, CoreWebVitalsAnalyzer, KeywordResearchAPI
from modules.microsoft_clarity import clarity_analyzer
from modules.ubersuggest_api import ubersuggest_api
from modules.advanced_monitoring import advanced_monitoring
from modules.performance_optimizer import performance_optimizer, DatabaseOptimizer
from modules.simple_backup import simple_backup
from modules.error_handler import (
    health_monitor, error_recovery, scheduler,
    async_retry_decorator
)
from config.settings import BotConfig, SourcesConfig, ContentConfig

class GamingNewsBot:
    """الوكيل البرمجي الرئيسي لأخبار الألعاب"""
    
    def __init__(self):
        self.is_running = False
        self.scraper = ContentScraper()
        self.youtube_analyzer = None
        self.advanced_youtube_analyzer = None
        self.video_approval_system = None
        self.content_generator = None
        self.publisher = None
        self.processed_articles_count = 0
        self.startup_time = datetime.now()
        self.web_interface_opened = False

        # تحميل الحالة المحفوظة
        self.load_saved_state()

        # إعداد معالجات الإشارات
        self.setup_signal_handlers()

    def open_web_interface(self):
        """فتح واجهة الويب تلقائياً"""
        if not self.web_interface_opened:
            def open_browser():
                time.sleep(3)  # انتظار 3 ثوان لضمان تشغيل الخادم
                url = "http://localhost:5000"
                try:
                    webbrowser.open(url)
                    logger.info(f"🌐 تم فتح واجهة الويب تلقائياً: {url}")
                    print(f"🌐 تم فتح واجهة الويب تلقائياً: {url}")
                    self.web_interface_opened = True
                except Exception as e:
                    logger.warning(f"⚠️ لم يتمكن من فتح المتصفح تلقائياً: {e}")
                    print(f"⚠️ لم يتمكن من فتح المتصفح تلقائياً: {e}")
                    print(f"📋 يرجى فتح المتصفح يدوياً والانتقال إلى: {url}")

            # بدء thread لفتح المتصفح
            browser_thread = threading.Thread(target=open_browser, daemon=True)
            browser_thread.start()

    def setup_signal_handlers(self):
        """إعداد معالجات إشارات النظام"""
        try:
            def signal_handler(signum, _):
                logger.info(f"📡 تم استلام إشارة {signum}، بدء الإيقاف الآمن...")
                asyncio.create_task(self.graceful_shutdown())

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        except ValueError as e:
            # هذا يحدث عندما لا نكون في الـ main thread
            logger.warning(f"⚠️ لا يمكن إعداد معالجات الإشارات: {e}")
            logger.info("ℹ️ سيتم الاعتماد على آليات الإيقاف الأخرى")
    
    def load_saved_state(self):
        """تحميل الحالة المحفوظة للبوت"""
        saved_state = error_recovery.load_state()
        
        if saved_state:
            self.processed_articles_count = saved_state.get('processed_articles_count', 0)
            logger.info(f"📂 تم تحميل الحالة المحفوظة: {self.processed_articles_count} مقال معالج")
    
    def save_current_state(self):
        """حفظ الحالة الحالية للبوت"""
        state_data = {
            'processed_articles_count': self.processed_articles_count,
            'startup_time': self.startup_time.isoformat(),
            'last_save_time': datetime.now().isoformat()
        }
        
        error_recovery.save_state(state_data)
    
    async def initialize(self):
        """تهيئة جميع مكونات البوت"""
        try:
            logger.start_session_log()
            logger.info("🚀 بدء تهيئة وكيل أخبار الألعاب...")
            
            # التحقق من التكوين
            if not BotConfig.validate_config():
                raise Exception("❌ التكوين غير مكتمل، يرجى التحقق من متغيرات البيئة")
            
            # تهيئة المولدات والناشرين
            await self._initialize_components()
            
            # اختبار الاتصالات
            await self._test_connections()
            
            # بدء مراقبة النظام
            health_monitor.start_monitoring()

            # بدء نظام المراقبة المتقدم
            advanced_monitoring.start_monitoring()
            logger.info("🔍 تم بدء نظام المراقبة المتقدم")

            # تحسين قاعدة البيانات
            DatabaseOptimizer.add_missing_indexes()
            DatabaseOptimizer.optimize_database()

            # إنشاء نسخة احتياطية أولية
            simple_backup.create_full_backup()

            logger.info("✅ تم تهيئة البوت بنجاح، جاهز للعمل!")

            return True
            
        except Exception as e:
            logger.critical("❌ فشل في تهيئة البوت", e)
            return False
    
    async def _initialize_components(self):
        """تهيئة المكونات الفردية"""

        # تهيئة محلل يوتيوب التقليدي
        self.youtube_analyzer = YouTubeAnalyzer()
        logger.info("✅ تم تهيئة محلل يوتيوب التقليدي")

        # تهيئة محلل يوتيوب المتقدم (الأولوية الأولى)
        self.advanced_youtube_analyzer = AdvancedYouTubeAnalyzer()
        logger.info("✅ تم تهيئة محلل يوتيوب المتقدم مع Whisper")

        # تهيئة نظام الموافقة على الفيديوهات (الويب)
        self.video_approval_system = web_approval_system
        logger.info("✅ تم تهيئة نظام الموافقة على الفيديوهات (الويب)")

        # تهيئة مولد المحتوى
        self.content_generator = ContentGenerator()
        logger.info("✅ تم تهيئة مولد المحتوى")

        # تهيئة الناشر (Blogger فقط - تم إزالة Telegram)
        blogger_config = {
            'client_id': BotConfig.BLOGGER_CLIENT_ID,
            'client_secret': BotConfig.BLOGGER_CLIENT_SECRET,
            'blog_id': BotConfig.BLOGGER_BLOG_ID
        }

        # إزالة Telegram - النشر على Blogger فقط
        self.publisher = PublisherManager(blogger_config, None)
        logger.info("✅ تم تهيئة مدير النشر (Blogger فقط - تم إزالة Telegram)")
    
    async def _test_connections(self):
        """اختبار جميع الاتصالات"""
        logger.info("🔍 اختبار الاتصالات...")
        
        # اختبار قاعدة البيانات
        stats = db.get_stats_summary(1)
        if not stats:
            raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        # اختبار الناشر
        connection_results = await self.publisher.test_all_connections()
        
        if not connection_results['overall']:
            logger.warning("⚠️ بعض الاتصالات لا تعمل، سيتم المتابعة مع المكونات المتاحة")
        
        logger.info("✅ اكتمل اختبار الاتصالات")
    
    async def run(self):
        """تشغيل البوت الرئيسي"""
        if not await self.initialize():
            logger.critical("❌ فشل في التهيئة، إيقاف البوت")
            return

        self.is_running = True
        logger.info("🤖 بدء تشغيل البوت، يعمل 24/7...")

        # فتح واجهة الويب تلقائياً
        print("🌐 فتح واجهة الويب للتحكم في الوكيل...")
        print("📋 الواجهة متاحة على: http://localhost:5000")
        self.open_web_interface()

        try:
            while self.is_running:
                cycle_result = await self._main_cycle()
                published_count = cycle_result.get('published_count', 0) if cycle_result else 0
                generated_articles = cycle_result.get('generated_articles', []) if cycle_result else []

                # حساب الوقت الأمثل للنشر التالي باستخدام الذكاء الاصطناعي
                logger.info("🧠 أليكس يحسب الوقت الأمثل للنشر التالي...")

                # تمرير بيانات المقالات المنشورة للتحليل
                article_data = None
                if published_count > 0 and generated_articles:
                    article_data = generated_articles[0]  # استخدام أول مقال كمرجع

                optimal_timing = await intelligent_cms.calculate_optimal_publishing_time(article_data)

                wait_time = optimal_timing.get('wait_seconds', 2 * 3600)  # افتراضي ساعتين
                wait_hours = optimal_timing.get('wait_hours', 2.0)
                confidence = optimal_timing.get('confidence_score', 50.0)
                reasoning = optimal_timing.get('reasoning', ['تحليل ذكي'])

                # تسجيل القرار الذكي
                logger.info(f"⏰ أليكس قرر: انتظار {wait_hours:.1f} ساعة للنشر التالي")
                logger.info(f"🎯 مستوى الثقة: {confidence:.1f}%")
                logger.info(f"💡 الأسباب: {', '.join(reasoning)}")

                if published_count > 0:
                    logger.info(f"✅ تم نشر {published_count} مقال - الوقت التالي محسوب ذكياً")
                else:
                    logger.info(f"⚠️ لم يتم نشر مقالات - سيعيد أليكس المحاولة خلال {wait_hours:.1f} ساعة")

                await asyncio.sleep(wait_time)
                
        except Exception as e:
            logger.critical("❌ خطأ حرج في التشغيل الرئيسي", e)
            await self._handle_critical_error(e)
        
        finally:
            await self.graceful_shutdown()
    
    async def _main_cycle(self):
        """دورة العمل الرئيسية المدعومة بالذكاء الاصطناعي المتقدم"""
        cycle_start_time = datetime.now()
        logger.info("🚀 بدء دورة عمل ذكية جديدة مع أليكس...")

        try:
            # حفظ الحالة قبل البدء
            self.save_current_state()

            # 0. اتخاذ قرارات استراتيجية ذكية
            logger.info("🧠 أليكس يحلل الوضع ويتخذ القرارات الاستراتيجية...")
            strategic_decisions = await intelligent_cms.make_content_decisions()

            if strategic_decisions:
                logger.info("💡 تم اتخاذ قرارات استراتيجية جديدة")

                # رد شخصي من أليكس حول القرارات
                decision_response = ai_personality.generate_personality_response(
                    "اتخاذ قرارات استراتيجية جديدة للمحتوى",
                    'analytical'
                )
                logger.info(f"🤖 أليكس: {decision_response}")

            # 1. البحث أولاً في قنوات YouTube المحددة (الأولوية الأولى)
            youtube_content = await self._collect_from_priority_youtube_channels()

            # 2. جمع المحتوى من المصادر الأخرى إذا لم نجد محتوى كافي من YouTube
            if youtube_content and len(youtube_content) > 0:
                collected_content = youtube_content
                logger.info(f"🎥 تم العثور على محتوى من YouTube: {len(collected_content)} عنصر")

                # إذا كان المحتوى من YouTube قليل، أضف محتوى من مصادر أخرى
                if len(youtube_content) < 3:
                    logger.info("🔄 المحتوى من YouTube قليل، إضافة محتوى من مصادر أخرى...")
                    additional_content = await self._collect_content_intelligently()
                    if additional_content:
                        collected_content.extend(additional_content[:2])  # أضف حتى 2 مقال إضافي
                        logger.info(f"✅ تم إضافة {len(additional_content[:2])} مقال إضافي من مصادر أخرى")
            else:
                logger.info("📺 لم يتم العثور على محتوى مناسب من YouTube، التحول للمصادر الأخرى...")
                collected_content = await self._collect_content_intelligently()

                # إذا لم نجد محتوى من أي مصدر، استخدم البحث العميق
                if not collected_content or len(collected_content) == 0:
                    logger.info("🔍 لم يتم العثور على محتوى من المصادر التقليدية، بدء البحث العميق...")
                    collected_content = await self._deep_search_for_content()

            # التحقق النهائي من وجود محتوى
            if not collected_content or len(collected_content) == 0:
                logger.warning("⚠️ لم يتم العثور على محتوى جديد للمعالجة من جميع المصادر")

                # تفعيل نظام البحث التاريخي الذكي
                logger.info("🔍 تفعيل نظام البحث التاريخي للعثور على محتوى قيم...")
                historical_content = await self._search_historical_content()

                if historical_content:
                    collected_content = historical_content
                    logger.info(f"✅ تم العثور على {len(collected_content)} عنصر من البحث التاريخي")
                else:
                    # محاولة أخيرة: إنشاء محتوى تلقائي
                    logger.info("🤖 محاولة إنشاء محتوى تلقائي...")
                    auto_content = await self._generate_automatic_content()

                    if auto_content:
                        collected_content = [auto_content]
                        logger.info("✅ تم إنشاء محتوى تلقائي بنجاح")
                    else:
                        logger.error("❌ فشل في العثور على أي محتوى - إنهاء الدورة")
                        return

                if historical_content:
                    logger.info(f"✅ تم العثور على {len(historical_content)} محتوى من البحث التاريخي")
                    collected_content = historical_content
                else:
                    # قرار ذكي للتعامل مع نقص المحتوى
                    no_content_decision = ai_personality.make_personality_driven_decision(
                        context={'situation': 'no_content_found', 'urgency': 'medium', 'impact': 'medium'},
                        options=[
                            {'name': 'create_trending_content', 'data_support': 9, 'user_benefit': 8, 'risk_level': 3},
                            {'name': 'create_evergreen_content', 'innovation_level': 6, 'long_term_impact': 8, 'risk_level': 2},
                            {'name': 'search_deeper_sources', 'data_support': 7, 'user_benefit': 6, 'risk_level': 4}
                        ]
                    )

                    chosen_action = no_content_decision.get('chosen_option', {}).get('option', {}).get('name', 'create_trending_content')
                    logger.info(f"🤖 أليكس قرر: {chosen_action}")

                    # تنفيذ قرار أليكس
                    if chosen_action == 'create_trending_content':
                        trending_content = await self._create_trending_content()
                        if trending_content:
                            collected_content = trending_content
                            logger.info(f"🔥 تم إنشاء {len(trending_content)} محتوى رائج")
                    elif chosen_action == 'search_deeper_sources':
                        deeper_content = await self._search_deeper_sources()
                        if deeper_content:
                            collected_content = deeper_content
                            logger.info(f"🔍 تم العثور على {len(deeper_content)} محتوى من مصادر عميقة")

                    if not collected_content:
                        alex_response = ai_personality.generate_personality_response(
                            "لم أجد محتوى مناسب، سأنتظر وأعيد المحاولة لاحقاً",
                            'patient'
                        )
                        logger.info(f"🤖 أليكس: {alex_response}")
                        scheduler.mark_successful_run()
                        return

            # 2. معالجة وتوليد المقالات مع تحسينات الجذب
            generated_articles = await self._process_content_with_engagement(collected_content)

            # 3. نشر المقالات مع تتبع الأداء
            published_count = await self._publish_articles_with_analytics(generated_articles)

            # 4. تحديث الإحصائيات والتحليلات المتقدمة
            self._update_statistics(len(collected_content), published_count)

            # 5. إنشاء تقرير تحليلي شامل
            comprehensive_report = await self._generate_comprehensive_report()

            # 6. مراقبة الأداء المتقدمة
            await self._run_performance_monitoring()

            # 6.1. تحديث إحصائيات الأداء
            performance_optimizer.stats['api_calls'] += len(collected_content)

            # 6.2. عرض تقرير المراقبة المتقدم
            monitoring_report = advanced_monitoring.generate_monitoring_report()
            logger.info(f"📊 تقرير المراقبة المتقدم:\n{monitoring_report}")

            # 7. تعلم من النتائج
            await self._learn_from_cycle_results(comprehensive_report, strategic_decisions)

            # 8. تقرير المصادر المعطلة
            self._log_failed_sources_report()

            # تسجيل نجاح الدورة
            cycle_duration = (datetime.now() - cycle_start_time).total_seconds()
            logger.info(f"✅ اكتملت الدورة الذكية بنجاح في {cycle_duration:.1f} ثانية")
            logger.info(f"📊 المحتوى المجمع: {len(collected_content)}, المنشور: {published_count}")

            # رد شخصي من أليكس
            success_response = ai_personality.generate_personality_response(
                f"نجاح دورة المحتوى مع نشر {published_count} مقالات",
                'enthusiastic'
            )
            logger.info(f"🤖 أليكس: {success_response}")

            scheduler.mark_successful_run()

            # إرجاع نتائج الدورة
            return {
                'published_count': published_count,
                'collected_count': len(collected_content),
                'generated_articles': generated_articles,
                'success': True
            }

        except Exception as e:
            logger.error("❌ خطأ في دورة العمل الرئيسية", e)
            scheduler.mark_failed_run(e)

            # محاولة الاستعادة
            await self._attempt_error_recovery(e)

            # إرجاع نتائج فاشلة
            return {
                'published_count': 0,
                'collected_count': 0,
                'generated_articles': [],
                'success': False
            }
    
    @async_retry_decorator(max_retries=2, base_delay=30)
    async def _collect_content(self) -> List[Dict]:
        """جمع المحتوى من جميع المصادر مع منطق الأولوية"""
        all_content = []
        
        try:
            # 1. البحث العميق المتقدم باستخدام Tavily (الأولوية الأولى) + APIs متعددة
            logger.info("� بدء البحث العميق المتقدم باستخدام Tavily و APIs متعددة...")

            # كلمات مفتاحية محسنة للبحث العميق
            advanced_keywords = [
                'gaming news today',
                'video game updates',
                'new game releases',
                'gaming industry news',
                'latest gaming announcements'
            ]

            advanced_content = []
            for keyword in advanced_keywords[:3]:  # أفضل 3 كلمات مفتاحية
                try:
                    # استخدام النظام المحسن الجديد مع النماذج الاحتياطية للذكاء الاصطناعي
                    logger.info(f"🚀 استخدام النظام المحسن مع النماذج الاحتياطية للبحث عن: {keyword}")

                    # المحاولة الأولى: النظام المحسن مع AI
                    enhanced_results = await self.scraper.enhanced_search_with_ai_fallbacks(keyword, 10, "balanced")
                    if enhanced_results:
                        advanced_content.extend(enhanced_results)
                        logger.info(f"✅ النظام المحسن مع AI: {len(enhanced_results)} نتيجة عالية الجودة لـ {keyword}")

                        # عرض معلومات إضافية عن النتائج
                        ai_enhanced_count = sum(1 for r in enhanced_results if r.get('ai_enhanced', False))
                        if ai_enhanced_count > 0:
                            logger.info(f"🤖 {ai_enhanced_count} نتيجة معززة بالذكاء الاصطناعي")
                    else:
                        # احتياطي: النظام المتقدم التقليدي
                        logger.info("🔄 النظام المحسن فشل، محاولة النظام المتقدم التقليدي...")

                        fallback_results = await self.scraper.advanced_search_with_fallbacks(keyword, 10)
                        if fallback_results:
                            advanced_content.extend(fallback_results)
                            logger.info(f"✅ النظام المتقدم التقليدي: {len(fallback_results)} نتيجة لـ {keyword}")
                        else:
                            # احتياطي أخير: النظام القديم
                            logger.info("🔄 النظام المتقدم فشل، محاولة النظام القديم...")

                            # أولاً: محاولة Tavily للبحث العميق
                            tavily_results = await self.scraper.advanced_search_and_extract_with_tavily(keyword, 8)
                            if tavily_results:
                                advanced_content.extend(tavily_results)
                                logger.info(f"🔍 Tavily (احتياطي): {len(tavily_results)} نتيجة لـ {keyword}")
                            else:
                                # احتياطي: SerpAPI
                                logger.info("🔄 Tavily فشل، محاولة استخدام SerpAPI...")
                                serpapi_results = await self.scraper.advanced_search_and_extract_with_serpapi(keyword, 10)
                                if serpapi_results:
                                    advanced_content.extend(serpapi_results)
                                    logger.info(f"🚀 SerpAPI (احتياطي): {len(serpapi_results)} نتيجة لـ {keyword}")
                                else:
                                    # احتياطي أخير: بحث Google التقليدي
                                    logger.info("🔄 SerpAPI فشل، محاولة استخدام Google Search...")
                                    google_results = self.scraper.extract_articles(keyword, "google_search")
                                    if google_results:
                                        advanced_content.extend(google_results)
                                        logger.info(f"🔍 Google Search (احتياطي): {len(google_results)} نتيجة لـ {keyword}")
                                    else:
                                        logger.warning(f"⚠️ فشلت جميع محركات البحث عن: {keyword}")

                    await asyncio.sleep(2)  # تأخير مناسب
                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث المتقدم عن {keyword}: {e}")
                    continue

            if advanced_content:
                logger.info(f"🚀 البحث المتقدم: تم العثور على {len(advanced_content)} مقال عالي الجودة")
                all_content.extend(advanced_content)

            # إذا لم يتم العثور على محتوى من البحث المتقدم، استخدم الطرق التقليدية
            if not all_content:
                logger.info("🤔 لم يتم العثور على محتوى من البحث المتقدم، التحول إلى الطرق التقليدية...")
                # 2. جمع من المواقع التقليدية كخطة بديلة
                website_content = await self._collect_from_websites()
                all_content.extend(website_content)
            else:
                website_content = [] # لضمان عدم تكرار المحتوى

            # 3. التحقق مما إذا كانت هناك مقالات حديثة
            thirty_days_ago = datetime.now() - timedelta(days=30)
            recent_articles = [
                article for article in website_content
                if article.get('published_date') and article['published_date'] > thirty_days_ago
            ]

            if recent_articles:
                logger.info(f"📰 تم العثور على {len(recent_articles)} مقال حديث من مواقع الويب التقليدية")
                all_content.extend(recent_articles)
            else:
                logger.info("🤔 لم يتم العثور على مقالات حديثة في المواقع، سيتم البحث في يوتيوب")
                # 4. إذا لم تكن هناك مقالات حديثة، ابحث في يوتيوب
                youtube_content = await self._collect_from_youtube()
                all_content.extend(youtube_content)
                # أضف المحتوى القديم من المواقع أيضًا، ربما يكون مفيدًا
                all_content.extend(website_content)

            # 4. تصفية المحتوى المكرر
            unique_content = await self._filter_duplicate_content(all_content)
            
            logger.info(f"📰 تم جمع {len(unique_content)} عنصر محتوى فريد")
            
            return unique_content
            
        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى", e)
            db.log_error("content_collection_error", str(e))
            raise
    
    async def _collect_from_websites(self) -> List[Dict]:
        """جمع المحتوى من المواقع الثابتة و RSS feeds"""
        website_content = []

        # 1. جمع من RSS feeds للحصول على أحدث الأخبار
        try:
            logger.info("📡 جمع المحتوى من RSS feeds...")
            rss_articles = self.scraper.extract_from_rss_feeds()
            if rss_articles:
                website_content.extend(rss_articles)
                logger.info(f"✅ تم جمع {len(rss_articles)} مقال من RSS feeds")
            else:
                logger.info("📭 لم يتم العثور على مقالات في RSS feeds")
        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى من RSS feeds", e)

        # 2. جمع من المصادر الثابتة
        logger.info("🌐 جمع المحتوى من المصادر الثابتة...")

        # إعادة تعيين المصادر المعطلة كل 24 ساعة
        if hasattr(self, '_last_reset_time'):
            if (datetime.now() - self._last_reset_time).total_seconds() > 86400:  # 24 ساعة
                self.scraper.reset_failed_sources()
                self._last_reset_time = datetime.now()
        else:
            self._last_reset_time = datetime.now()

        # المواقع الرسمية مع تحسينات
        logger.info("🌐 جمع المحتوى من المواقع الرسمية...")
        for url in SourcesConfig.OFFICIAL_SOURCES[:5]:  # أفضل 5 مواقع
            try:
                articles = self.scraper.extract_articles(url, "official_site")
                if articles:
                    # فلترة المقالات الحديثة فقط
                    recent_articles = [
                        article for article in articles
                        if self._is_article_recent(article)
                    ]
                    website_content.extend(recent_articles)
                    logger.info(f"✅ تم جمع {len(recent_articles)} مقال حديث من {url}")
                else:
                    logger.info(f"📭 لم يتم العثور على مقالات في {url}")
                await asyncio.sleep(2)  # تأخير أطول للاحترام
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        # مواقع الألعاب
        for url in SourcesConfig.GAMING_SITES:
            try:
                articles = self.scraper.extract_articles(url, "gaming_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        # المواقع العربية
        for url in SourcesConfig.ARABIC_SITES:
            try:
                articles = self.scraper.extract_articles(url, "arabic_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)

        # مواقع المراجعات
        for url in SourcesConfig.REVIEW_SITES:
            try:
                articles = self.scraper.extract_articles(url, "review_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)

        # المنتديات
        for url in SourcesConfig.FORUM_SITES:
            try:
                articles = self.scraper.extract_articles(url, "forum_site")
                website_content.extend(articles)
                await asyncio.sleep(1)
            except Exception as e:
                logger.warning(f"⚠️ فشل في جمع المحتوى من {url}", e)
        
        logger.info(f"📊 إجمالي المحتوى المجمع: {len(website_content)} مقال")
        return website_content

    def _is_article_recent(self, article: Dict) -> bool:
        """فحص ما إذا كان المقال حديث (آخر 7 أيام)"""
        try:
            published_date = article.get('published_date')
            if not published_date:
                return True  # إذا لم يكن هناك تاريخ، اعتبره حديث

            if isinstance(published_date, str):
                from dateutil import parser
                published_date = parser.parse(published_date)

            days_old = (datetime.now() - published_date).days

            # مقال حديث إذا كان أقل من 7 أيام أو يحتوي على كلمات دالة على الحداثة
            if days_old <= 7:
                return True

            # فحص إضافي للكلمات الدالة على الحداثة في العنوان أو الملخص
            text_to_check = f"{article.get('title', '')} {article.get('summary', '')}".lower()
            recent_indicators = [
                'today', 'yesterday', 'this week', 'recently', 'just announced',
                'breaking', 'latest', 'new', 'updated', 'fresh', 'current',
                '2025', 'january 2025', 'this month'
            ]

            return any(indicator in text_to_check for indicator in recent_indicators)

        except Exception as e:
            logger.debug(f"خطأ في فحص حداثة المقال: {e}")
            return True  # في حالة الخطأ، اعتبره حديث
    
    async def _collect_from_youtube(self) -> List[Dict]:
        """جمع المحتوى من يوتيوب"""
        try:
            videos = self.youtube_analyzer.search_videos(max_results=10)
            
            youtube_content = []
            for video in videos:
                article = self.youtube_analyzer.generate_article_from_video(video, BotConfig.TXTIFY_API_URL)
                if article:
                    youtube_content.append(article)
            
            return youtube_content
            
        except Exception as e:
            logger.warning("⚠️ فشل في جمع المحتوى من يوتيوب", e)
            return []
    
    async def _filter_duplicate_content(self, content_list: List[Dict]) -> List[Dict]:
        """تصفية المحتوى المكرر بخوارزمية محسنة"""
        unique_content = []
        seen_titles = set()
        seen_content_hashes = set()

        for content in content_list:
            try:
                title = content.get('title', '').lower().strip()
                content_text = content.get('content', '')

                # تنظيف العنوان للمقارنة
                clean_title = re.sub(r'[^\w\s]', '', title)
                clean_title = re.sub(r'\s+', ' ', clean_title).strip()

                # فحص التكرار المحلي أولاً (في نفس الدورة)
                if clean_title in seen_titles:
                    logger.debug(f"⏭️ تخطي محتوى مكرر محلياً: {title}")
                    continue

                # فحص تجزئة المحتوى
                content_hash = hashlib.md5(content_text.encode('utf-8')).hexdigest()
                if content_hash in seen_content_hashes:
                    logger.debug(f"⏭️ تخطي محتوى مكرر (نفس المحتوى): {title}")
                    continue

                # فحص التكرار في قاعدة البيانات
                is_duplicate, reason = db.is_duplicate_content(
                    content_text,
                    content.get('title', ''),
                    content.get('keywords', [])
                )

                if not is_duplicate:
                    # فحص جودة المحتوى
                    if self._is_quality_content(content):
                        unique_content.append(content)
                        seen_titles.add(clean_title)
                        seen_content_hashes.add(content_hash)
                    else:
                        logger.debug(f"⏭️ تخطي محتوى منخفض الجودة: {title}")
                else:
                    logger.debug(f"⏭️ تخطي محتوى مكرر في قاعدة البيانات: {title} - {reason}")

            except Exception as e:
                logger.warning(f"⚠️ خطأ في فحص التكرار للمحتوى: {content.get('title', 'غير محدد')}", e)
                # في حالة الخطأ، أضف المحتوى للأمان إذا كان يبدو جيداً
                if self._is_quality_content(content):
                    unique_content.append(content)

        return unique_content

    def _is_quality_content(self, content: Dict) -> bool:
        """فحص جودة المحتوى قبل الإضافة"""
        title = content.get('title', '')
        content_text = content.get('content', '')

        # فحوصات الجودة الأساسية
        if len(title) < 10:
            return False

        if len(content_text) < 100:
            return False

        # فحص وجود كلمات مفتاحية متعلقة بالألعاب
        gaming_keywords = [
            'game', 'gaming', 'player', 'console', 'pc', 'mobile',
            'لعبة', 'ألعاب', 'لاعب', 'جهاز', 'كمبيوتر', 'موبايل'
        ]

        text_to_check = f"{title} {content_text}".lower()
        has_gaming_content = any(keyword in text_to_check for keyword in gaming_keywords)

        return has_gaming_content
    
    @async_retry_decorator(max_retries=2, base_delay=10)
    async def _process_content(self, content_list: List[Dict]) -> List[Dict]:
        """معالجة وتوليد المقالات"""
        generated_articles = []
        
        for content in content_list[:1]:  # معالجة مقال واحد فقط لكل دورة (كل 3 ساعات)
            try:
                # استخدام اللهجة الافتراضية من الإعدادات
                dialect = ContentConfig.DEFAULT_DIALECT
                
                # توليد المقال
                article = self.content_generator.generate_article(
                    content, content.get('content_type', 'أخبار_عامة'), dialect
                )
                
                if article and 'error' not in article:
                    # مراجعة جودة المقال
                    quality_review = article.get('quality_review', {})

                    # فحص التطابق بين العنوان والمحتوى (أولوية قصوى)
                    if not quality_review.get('title_content_match', True):
                        logger.warning(f"❌ رفض مقال بسبب عدم التطابق بين العنوان والمحتوى: {article['title']}")
                        logger.info(f"💡 مشاكل التطابق: {quality_review.get('issues', [])}")
                        continue

                    # فحص ما إذا كان المقال يحتاج تحسين
                    if not quality_review.get('approved', True):
                        logger.warning(f"⚠️ مقال بجودة منخفضة: {article['title']}")
                        logger.info(f"💡 مشاكل الجودة: {quality_review.get('issues', [])}")

                        # يمكن إضافة منطق لإعادة توليد المقال أو تخطيه
                        if len(quality_review.get('issues', [])) > 3:
                            logger.warning(f"⏭️ تخطي مقال بجودة منخفضة جداً: {article['title']}")
                            continue

                    # إنشاء صورة واحدة عالية الجودة باستخدام النظام الذكي
                    logger.info(f"🎨 إنشاء صورة عالية الجودة باستخدام النظام الذكي للمقال: {article['title']}")

                    # استخدام النظام الذكي الجديد
                    from modules.smart_image_manager import smart_image_manager

                    smart_image = await smart_image_manager.generate_smart_image_for_article(article)

                    if smart_image:
                        # إضافة الصورة الواحدة عالية الجودة
                        article['image_urls'] = [smart_image['url']]
                        article['image_metadata'] = [smart_image]

                        # استخدام نفس الصورة كصورة مصغرة
                        article['thumbnail_url'] = smart_image['url']
                        article['thumbnail_metadata'] = smart_image

                        logger.info(f"✅ تم إنشاء صورة واحدة عالية الجودة للمقال (Smart AI Generated)")

                        # عرض إحصائيات الاستخدام
                        daily_stats = smart_image_manager.get_daily_stats()
                        logger.info(f"📊 إحصائيات اليوم: {daily_stats['images_generated']}/{daily_stats['policy']['max_daily_generations']} صورة، معدل التخزين المؤقت: {daily_stats['cache_hit_rate']:.1f}%")

                    else:
                        # العودة للطريقة التقليدية كخطة بديلة (صورة واحدة فقط)
                        logger.warning("⚠️ لم يتم إنشاء صورة ذكية، استخدام الطريقة التقليدية...")

                        # إنشاء صورة واحدة فقط
                        if article.get('image_prompts'):
                            image_data = await self.content_generator.generate_image_with_metadata(
                                article['image_prompts'][0], article['title']
                            )
                            if image_data:
                                article['image_urls'] = [image_data['url']]
                                article['image_metadata'] = [image_data]
                                article['thumbnail_url'] = image_data['url']
                                article['thumbnail_metadata'] = image_data
                                logger.info(f"✅ تم إضافة صورة واحدة تقليدية للمقال")

                    # تحسين للـ SEO
                    optimized_article = self.content_generator.optimize_for_seo(article)

                    # مراجعة نهائية للجودة
                    final_quality_score = optimized_article.get('seo_score', 0)
                    if final_quality_score >= 60:  # حد أدنى للجودة
                        generated_articles.append(optimized_article)
                        logger.info(f"✅ تم قبول مقال بجودة {final_quality_score}%: {article['title']}")
                    else:
                        logger.warning(f"❌ رفض مقال بجودة منخفضة {final_quality_score}%: {article['title']}")

                    # تأخير بين التوليدات
                    await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"❌ فشل في توليد مقال للمحتوى: {content.get('title', 'غير محدد')}", e)
        
        return generated_articles
    
    @async_retry_decorator(max_retries=3, base_delay=15)
    async def _publish_articles(self, articles: List[Dict]) -> int:
        """نشر المقالات على جميع المنصات"""
        published_count = 0
        
        for article in articles:
            try:
                # نشر المقال
                results = await self.publisher.publish_complete_article(article)
                
                if results['success']:
                    # حفظ في قاعدة البيانات
                    article_data = {
                        **article,
                        'blogger_url': results['blogger_url'],
                        'telegram_message_id': results['telegram_message_ids'][0] if results['telegram_message_ids'] else None
                    }

                    article_id = db.save_article(article_data)

                    # تحديث روابط المقال
                    if article_id:
                        db.update_article_urls(
                            article_id,
                            results['blogger_url'],
                            results['telegram_message_ids'][0] if results['telegram_message_ids'] else None
                        )

                        # تحليل أداء المقال
                        article_with_id = {**article, 'id': article_id}
                        performance_data = analytics.analyze_article_performance(article_with_id)

                        if performance_data:
                            logger.info(f"📊 تحليل الأداء: جودة {performance_data['quality_score']:.1f}%, SEO {performance_data['seo_score']:.1f}%, تفاعل {performance_data['engagement_potential']:.1f}%")

                    published_count += 1
                    self.processed_articles_count += 1

                    logger.info(f"✅ تم نشر المقال بنجاح: {article['title']}")

                    # تأخير بين النشر
                    await asyncio.sleep(10)
                
                else:
                    logger.error(f"❌ فشل في نشر المقال: {article['title']}")
                    logger.error(f"الأخطاء: {results['errors']}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في نشر المقال: {article.get('title', 'غير محدد')}", e)
        
        return published_count
    
    def _update_statistics(self, processed_count: int, published_count: int):
        """تحديث إحصائيات الأداء"""
        db.update_performance_stats(
            articles_processed=processed_count,
            articles_published=published_count
        )
        
        # تسجيل الإحصائيات
        stats = db.get_stats_summary(1)
        logger.info(f"📊 إحصائيات اليوم: {stats}")
    
    
    async def _handle_critical_error(self, error: Exception):
        """التعامل مع الأخطاء الحرجة"""
        logger.critical("🚨 خطأ حرج في النظام", error)
        
        # حفظ الحالة
        self.save_current_state()
        
        # محاولة الاستعادة
        recovery_success = await self._attempt_error_recovery(error)
        
        if not recovery_success:
            logger.critical("❌ فشل في الاستعادة من الخطأ الحرج، إيقاف البوت")
            self.is_running = False
    
    async def _attempt_error_recovery(self, error: Exception) -> bool:
        """محاولة الاستعادة من خطأ"""
        error_type = self._classify_error(error)
        
        error_details = {
            'error_message': str(error),
            'error_type': type(error).__name__,
            'traceback': traceback.format_exc(),
            'timestamp': datetime.now().isoformat()
        }
        
        return error_recovery.attempt_recovery(error_type, error_details)
    
    def _classify_error(self, error: Exception) -> str:
        """تصنيف نوع الخطأ"""
        error_type_name = type(error).__name__.lower()
        error_message = str(error).lower()

        # أخطاء تيليجرام
        if any(keyword in error_message for keyword in ['chat not found', 'unauthorized', 'forbidden', 'telegram']):
            return 'telegram_error'
        elif 'database' in error_message or 'sqlite' in error_type_name:
            return 'database_error'
        elif 'api' in error_message or 'http' in error_type_name:
            return 'api_error'
        elif 'network' in error_message or 'connection' in error_message:
            return 'network_error'
        elif 'memory' in error_type_name:
            return 'memory_error'
        else:
            return 'general_error'
    
    async def graceful_shutdown(self):
        """إيقاف آمن للبوت"""
        logger.info("🛑 بدء الإيقاف الآمن للبوت...")
        
        self.is_running = False
        
        try:
            # حفظ الحالة النهائية
            self.save_current_state()
            
            # إيقاف مراقبة النظام
            health_monitor.stop_monitoring()

            # إيقاف نظام المراقبة المتقدم
            advanced_monitoring.stop_monitoring()

            # إنشاء نسخة احتياطية نهائية
            logger.info("💾 إنشاء نسخة احتياطية نهائية...")
            simple_backup.create_full_backup()

            # تنظيف الكاش
            performance_optimizer.clear_expired_cache()

            # تنظيف الموارد
            # (إغلاق الاتصالات، الملفات، إلخ)
            
            # تسجيل إحصائيات نهائية
            uptime = (datetime.now() - self.startup_time).total_seconds() / 3600
            logger.info(f"📊 إحصائيات نهائية:")
            logger.info(f"   • وقت التشغيل: {uptime:.1f} ساعة")
            logger.info(f"   • المقالات المعالجة: {self.processed_articles_count}")
            
            logger.end_session_log()
            
        except Exception as e:
            logger.error("❌ خطأ أثناء الإيقاف الآمن", e)
        
        finally:
            logger.info("✅ تم إيقاف البوت بنجاح")

    async def _collect_content_intelligently(self) -> List[Dict]:
        """جمع المحتوى بطريقة ذكية"""
        try:
            # استخدام الطريقة الأصلية مع تحسينات
            collected_content = await self._collect_content()

            # تحليل جودة المحتوى المجمع
            if collected_content:
                quality_analysis = []
                for content in collected_content:
                    # فحص جودة المحتوى
                    is_quality = self._is_quality_content(content)
                    if is_quality:
                        quality_analysis.append(content)

                logger.info(f"🔍 تم فلترة {len(quality_analysis)} محتوى عالي الجودة من {len(collected_content)}")
                return quality_analysis

            return collected_content

        except Exception as e:
            logger.error("❌ فشل في جمع المحتوى الذكي", e)
            return []

    async def _process_content_with_engagement(self, content_list: List[Dict]) -> List[Dict]:
        """معالجة المحتوى مع تحسينات الجذب"""
        try:
            # استخدام الطريقة الأصلية مع تحسينات
            generated_articles = await self._process_content(content_list)

            # تطبيق تحسينات الجذب
            for article in generated_articles:
                # تحسين العنوان للجذب
                if 'title' in article:
                    article['title'] = engagement_engine._optimize_title_for_virality(article['title'], 'gaming')

                # إضافة عناصر تفاعلية
                if 'content' in article:
                    article['content'] = engagement_engine.add_interactive_elements(article['content'])

            logger.info(f"✨ تم تحسين {len(generated_articles)} مقال للجذب")
            return generated_articles

        except Exception as e:
            logger.error("❌ فشل في معالجة المحتوى مع الجذب", e)
            return await self._process_content(content_list)  # العودة للطريقة الأصلية

    async def _publish_articles_with_analytics(self, articles: List[Dict]) -> int:
        """نشر المقالات مع تتبع التحليلات"""
        try:
            # النشر الأصلي
            published_count = await self._publish_articles(articles)

            # تتبع التحليلات
            for article in articles:
                if article.get('published', False):
                    # تسجيل بيانات النشر
                    visitor_analytics.track_content_publication(
                        article.get('title', ''),
                        article.get('url', ''),
                        article.get('category', 'gaming')
                    )

            logger.info(f"📊 تم تتبع تحليلات {published_count} مقال منشور")
            return published_count

        except Exception as e:
            logger.error("❌ فشل في النشر مع التحليلات", e)
            return await self._publish_articles(articles)  # العودة للطريقة الأصلية

    async def _generate_comprehensive_report(self) -> Dict:
        """إنشاء تقرير تحليلي شامل"""
        try:
            # جمع البيانات من مصادر مختلفة
            db_stats = db.get_stats_summary(7)  # آخر 7 أيام
            analytics_data = analytics.get_analytics_summary()
            visitor_data = visitor_analytics.get_visitor_insights()

            report = {
                'timestamp': datetime.now().isoformat(),
                'database_stats': db_stats,
                'analytics': analytics_data,
                'visitor_insights': visitor_data,
                'recommendations': []
            }

            # إضافة توصيات ذكية
            if db_stats.get('articles_published', 0) < 5:
                report['recommendations'].append("زيادة معدل النشر اليومي")

            if analytics_data.get('engagement_rate', 0) < 0.3:
                report['recommendations'].append("تحسين جودة المحتوى للجذب")

            logger.info("📋 تم إنشاء التقرير التحليلي الشامل")
            return report

        except Exception as e:
            logger.error("❌ فشل في إنشاء التقرير الشامل", e)
            return {}

    async def _learn_from_cycle_results(self, report: Dict, decisions: Dict):
        """التعلم من نتائج الدورة"""
        try:
            # تحليل الأداء
            performance_score = 0

            if report.get('database_stats', {}).get('articles_published', 0) > 0:
                performance_score += 30

            if report.get('analytics', {}).get('engagement_rate', 0) > 0.2:
                performance_score += 40

            if len(report.get('recommendations', [])) < 3:
                performance_score += 30

            # حفظ الدروس المستفادة
            learning_data = {
                'cycle_timestamp': datetime.now().isoformat(),
                'performance_score': performance_score,
                'decisions_made': decisions,
                'results_achieved': report,
                'lessons_learned': []
            }

            if performance_score >= 70:
                learning_data['lessons_learned'].append("دورة ناجحة - الحفاظ على الاستراتيجية")
            else:
                learning_data['lessons_learned'].append("دورة تحتاج تحسين - مراجعة الاستراتيجية")

            # حفظ في قاعدة البيانات
            db.save_learning_data(learning_data)

            logger.info(f"🧠 تم التعلم من الدورة - نقاط الأداء: {performance_score}/100")

        except Exception as e:
            logger.error("❌ فشل في التعلم من نتائج الدورة", e)

    async def _search_historical_content(self) -> List[Dict]:
        """البحث عن محتوى تاريخي قيم من الأسبوع والشهر الماضي"""
        try:
            logger.info("📅 بدء البحث التاريخي للمحتوى...")

            historical_content = []

            # كلمات بحث تاريخية محسنة ومتنوعة
            current_week = datetime.now().strftime("week of %B %d, %Y")
            last_week = (datetime.now() - timedelta(days=7)).strftime("week of %B %d, %Y")

            historical_queries = [
                f"gaming news {current_week}",
                f"video game updates {last_week}",
                "new game releases January 2025",
                "gaming announcements this month",
                "latest video game trailers 2025",
                "indie game news recent",
                "AAA games updates January",
                "gaming industry developments 2025",
                "esports news this week",
                "mobile gaming updates recent",
                "PC gaming news January 2025",
                "console gaming announcements",
                "VR gaming developments 2025",
                "game developer interviews recent",
                "gaming technology news 2025"
            ]

            # استخدام المزيد من الاستعلامات للبحث التاريخي
            for query in historical_queries[:10]:  # زيادة عدد الاستعلامات
                try:
                    logger.info(f"🔍 البحث التاريخي المحسن عن: {query}")

                    # استخدام النظام المحسن للبحث
                    search_results = self.scraper.search_and_extract_articles(query, num_results=6)

                    if search_results:
                        for article in search_results[:3]:  # أفضل 3 نتائج لكل استعلام
                            try:
                                if article and len(article.get('content', '')) > 300:
                                    # فحص جودة المحتوى
                                    content_quality = article.get('content_quality', 0)
                                    if content_quality >= 6:  # جودة جيدة
                                        # إضافة معلومات البحث التاريخي
                                        article['source_type'] = 'historical_search'
                                        article['search_query'] = query
                                        article['historical_relevance'] = 'high'
                                        article['discovery_method'] = 'enhanced_historical_search'
                                        historical_content.append(article)
                                        logger.debug(f"✅ مقال عالي الجودة: {article.get('title', '')[:50]}...")
                                    else:
                                        logger.debug(f"⏭️ تخطي مقال منخفض الجودة: {article.get('title', '')[:50]}...")

                            except Exception as e:
                                continue

                    # تأخير أطول بين الطلبات لتجنب الحظر
                    await asyncio.sleep(3)

                except Exception as e:
                    logger.warning(f"⚠️ فشل في البحث التاريخي عن {query}: {e}")
                    continue

            # إزالة المحتوى المكرر
            unique_historical = []
            seen_titles = set()

            for content in historical_content:
                title = content.get('title', '').lower()
                if title and title not in seen_titles and len(title) > 10:
                    seen_titles.add(title)
                    unique_historical.append(content)

            logger.info(f"📚 تم العثور على {len(unique_historical)} محتوى تاريخي فريد")
            return unique_historical[:6]  # أفضل 6 محتويات

        except Exception as e:
            logger.error("❌ فشل في البحث التاريخي", e)
            return []

    async def _collect_from_priority_youtube_channels(self) -> List[Dict]:
        """جمع المحتوى من قنوات YouTube المحددة بالأولوية"""
        try:
            logger.info("🎥 بدء البحث في قنوات YouTube المحددة...")

            if not self.advanced_youtube_analyzer:
                logger.warning("⚠️ محلل YouTube المتقدم غير متاح")
                return []

            # البحث عن أحدث فيديو مناسب
            video_data = await self.advanced_youtube_analyzer.find_latest_gaming_video()

            if not video_data:
                logger.info("📭 لم يتم العثور على فيديو مناسب في القنوات المحددة")
                return []

            # استخراج النص من الفيديو باستخدام Whisper أولاً
            logger.info("🎤 بدء استخراج النص من الفيديو للمراجعة...")
            transcript = await self.advanced_youtube_analyzer.extract_video_transcript_with_whisper(video_data['id'])

            # طلب الموافقة على الفيديو مع النص المستخرج
            approval_result = await self._request_video_approval(video_data, transcript)

            if not approval_result['approved']:
                logger.info(f"❌ تم رفض الفيديو: {approval_result['reason']}")
                return []

            if not transcript or len(transcript.strip()) < 50:
                logger.error("❌ فشل في استخراج النص من الفيديو أو النص قصير جداً")
                # محاولة استخدام طريقة بديلة
                logger.info("🔄 محاولة استخدام طريقة بديلة لاستخراج المحتوى...")
                return await self._extract_video_content_alternative(video_data)

            # تحليل النص للبحث عن أخبار الألعاب
            channel_info = video_data.get('channel_info', {})
            language = channel_info.get('language', 'ar')
            analysis_result = self.advanced_youtube_analyzer.analyze_transcript_for_gaming_news(transcript, language)

            # تحويل النتائج إلى تنسيق المحتوى المطلوب
            content_items = []

            # إضافة الأخبار الرئيسية
            for news_item in analysis_result['main_news']:
                content_item = {
                    'title': self._generate_title_from_news(news_item['text'], video_data['title']),
                    'content': news_item['text'],
                    'summary': news_item['text'][:200] + "...",
                    'url': f"https://youtube.com/watch?v={video_data['id']}",
                    'source': f"YouTube - {channel_info.get('name', 'Unknown')}",
                    'source_type': 'youtube_video',
                    'keywords': news_item['topics'],
                    'published_date': video_data.get('published_at'),
                    'video_id': video_data['id'],
                    'video_title': video_data['title'],
                    'channel_name': channel_info.get('name', ''),
                    'importance_score': news_item['importance'],
                    'content_type': 'أخبار_الألعاب',
                    'language': language
                }
                content_items.append(content_item)

            # إضافة المعلومات الإضافية كمحتوى إذا لم نجد أخبار كافية
            if len(content_items) < 2 and analysis_result['additional_info']:
                logger.info("🔄 إضافة المعلومات الإضافية كمحتوى...")
                for info_item in analysis_result['additional_info'][:3]:  # أفضل 3 معلومات
                    content_item = {
                        'title': self._generate_title_from_news(info_item['text'], video_data['title']),
                        'content': info_item['text'],
                        'summary': info_item['text'][:200] + "...",
                        'url': f"https://youtube.com/watch?v={video_data['id']}",
                        'source': f"YouTube - {channel_info.get('name', 'Unknown')}",
                        'source_type': 'youtube_video',
                        'keywords': self._extract_keywords_from_text(info_item['text']),
                        'published_date': video_data.get('published_at'),
                        'video_id': video_data['id'],
                        'video_title': video_data['title'],
                        'channel_name': channel_info.get('name', ''),
                        'importance_score': info_item.get('relevance', 50),
                        'content_type': 'معلومات_الألعاب',
                        'language': language
                    }
                    content_items.append(content_item)

            # حفظ بيانات الفيديو المعالج
            await self._save_processed_video_data(video_data, transcript, analysis_result)

            logger.info(f"✅ تم استخراج {len(content_items)} خبر من فيديو YouTube")
            return content_items

        except Exception as e:
            logger.error(f"❌ خطأ في جمع المحتوى من YouTube: {e}")
            return []

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """استخراج الكلمات المفتاحية من النص"""
        try:
            keywords = []
            text_lower = text.lower()

            # كلمات مفتاحية متعلقة بالألعاب
            gaming_keywords = [
                'game', 'gaming', 'لعبة', 'ألعاب', 'minecraft', 'fortnite', 'steam',
                'playstation', 'xbox', 'nintendo', 'pc', 'mobile', 'update', 'تحديث',
                'new', 'جديد', 'release', 'إصدار', 'review', 'مراجعة'
            ]

            for keyword in gaming_keywords:
                if keyword in text_lower:
                    keywords.append(keyword)

            return keywords[:5]  # أفضل 5 كلمات مفتاحية

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الكلمات المفتاحية: {e}")
            return ['gaming', 'ألعاب']

    async def _extract_video_content_alternative(self, video_data: Dict) -> List[Dict]:
        """طريقة بديلة لاستخراج المحتوى من الفيديو"""
        try:
            logger.info("🔄 استخدام طريقة بديلة لاستخراج المحتوى...")

            # استخدام العنوان والوصف كمحتوى
            title = video_data.get('title', '')
            description = video_data.get('description', '')

            if not title and not description:
                logger.warning("⚠️ لا يوجد عنوان أو وصف للفيديو")
                return []

            # إنشاء محتوى مبسط من البيانات المتاحة
            content = f"{title}\n\n{description}"

            # تحليل المحتوى للبحث عن أخبار الألعاب
            analysis_result = self.advanced_youtube_analyzer.analyze_transcript_for_gaming_news(content, 'ar')

            if analysis_result and analysis_result.get('gaming_news'):
                logger.info("✅ تم استخراج محتوى بديل بنجاح")
                return analysis_result['gaming_news']
            else:
                logger.info("📭 لم يتم العثور على أخبار ألعاب في المحتوى البديل")
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في الطريقة البديلة: {e}")
            return []

    async def _deep_search_for_content(self) -> List[Dict]:
        """البحث العميق عن المحتوى باستخدام مصادر متعددة"""
        try:
            logger.info("🔍 بدء البحث العميق المتقدم باستخدام Tavily و APIs متعددة...")

            # قائمة مصطلحات البحث المتنوعة
            search_terms = [
                "gaming news today",
                "latest video game updates",
                "new game releases 2025",
                "gaming industry news",
                "esports news today",
                "game reviews latest",
                "gaming technology news",
                "indie games news"
            ]

            all_results = []

            # البحث باستخدام كل مصطلح
            for term in search_terms[:3]:  # حد أقصى 3 مصطلحات لتوفير الوقت
                try:
                    logger.info(f"🔍 البحث العميق في Tavily عن: '{term}' (عمق: advanced)")

                    # استخدام Tavily للبحث العميق
                    search_results = None
                    if hasattr(self, 'tavily_client') and self.tavily_client:
                        try:
                            search_results = self.tavily_client.search(
                                query=term,
                                search_depth="advanced",
                                max_results=5,
                                include_domains=["ign.com", "gamespot.com", "polygon.com", "kotaku.com", "pcgamer.com"]
                            )
                        except Exception as tavily_error:
                            logger.warning(f"⚠️ فشل في استخدام Tavily: {tavily_error}")

                    # إذا فشل Tavily، استخدم البحث العادي
                    if not search_results and hasattr(self, 'scraper'):
                        try:
                            logger.info(f"🔄 استخدام البحث العادي بدلاً من Tavily...")
                            search_results = {'results': []}

                            # البحث في مصادر الأخبار المعروفة
                            gaming_sources = [
                                "https://www.ign.com/news",
                                "https://www.gamespot.com/news/",
                                "https://www.polygon.com/gaming",
                                "https://kotaku.com/",
                                "https://www.pcgamer.com/news/"
                            ]

                            for source in gaming_sources[:2]:  # أول مصدرين فقط
                                try:
                                    articles = self.scraper.extract_articles(source, "news_source")
                                    if articles:
                                        for article in articles[:2]:  # أول مقالين من كل مصدر
                                            search_results['results'].append({
                                                'title': article.get('title', ''),
                                                'content': article.get('content', ''),
                                                'url': article.get('url', source)
                                            })
                                except Exception as scraper_error:
                                    logger.warning(f"⚠️ فشل في استخراج من {source}: {scraper_error}")
                                    continue

                        except Exception as fallback_error:
                            logger.warning(f"⚠️ فشل في البحث البديل: {fallback_error}")

                        if search_results and 'results' in search_results:
                            for result in search_results['results']:
                                # تحويل نتيجة البحث إلى تنسيق مقال
                                article = {
                                    'title': result.get('title', ''),
                                    'content': result.get('content', ''),
                                    'url': result.get('url', ''),
                                    'summary': result.get('content', '')[:300] + '...',
                                    'source': 'Tavily Deep Search',
                                    'keywords': [term],
                                    'timestamp': datetime.now().isoformat()
                                }

                                if len(article['content']) > 100:  # تأكد من وجود محتوى كافي
                                    all_results.append(article)
                                    logger.info(f"✅ تم العثور على مقال: {article['title'][:50]}...")

                    # إضافة تأخير قصير بين البحثات
                    await asyncio.sleep(1)

                except Exception as search_error:
                    logger.warning(f"⚠️ فشل في البحث عن '{term}': {search_error}")
                    continue

            if all_results:
                logger.info(f"✅ تم العثور على {len(all_results)} مقال من البحث العميق")
                return all_results[:5]  # إرجاع أفضل 5 نتائج
            else:
                logger.warning("⚠️ لم يتم العثور على أي محتوى من البحث العميق")
                return []

        except Exception as e:
            logger.error(f"❌ خطأ في البحث العميق: {e}")
            return []

    async def _generate_automatic_content(self) -> Optional[Dict]:
        """إنشاء محتوى تلقائي عند عدم وجود مصادر"""
        try:
            logger.info("🤖 بدء إنشاء محتوى تلقائي...")

            # قائمة مواضيع الألعاب الشائعة والحديثة
            gaming_topics = [
                {
                    'title': "أفضل الألعاب المجانية لعام 2025",
                    'content': """
                    مع بداية عام 2025، تشهد صناعة الألعاب المجانية نمواً هائلاً وتطوراً مستمراً.

                    أبرز الألعاب المجانية هذا العام:
                    - ألعاب Battle Royale الجديدة مع تقنيات متطورة
                    - ألعاب MMORPG مع عوالم مفتوحة ضخمة
                    - ألعاب الهواتف الذكية عالية الجودة
                    - ألعاب التعاون الجماعي المبتكرة

                    هذه الألعاب تقدم تجارب غنية ومتنوعة للاعبين من جميع الأعمار والاهتمامات.
                    """
                },
                {
                    'title': "تطورات صناعة الألعاب في 2025",
                    'content': """
                    تشهد صناعة الألعاب تطورات مثيرة في عام 2025 مع ظهور تقنيات جديدة.

                    أهم التطورات:
                    - تقنيات الذكاء الاصطناعي في تطوير الألعاب
                    - الواقع المعزز والافتراضي في الألعاب
                    - الألعاب السحابية وتطورها
                    - منصات الألعاب الجديدة والمبتكرة

                    هذه التطورات تعد بمستقبل مشرق لعالم الألعاب والترفيه التفاعلي.
                    """
                },
                {
                    'title': "نصائح للاعبين المبتدئين في عالم الألعاب",
                    'content': """
                    للاعبين الجدد في عالم الألعاب، إليكم أهم النصائح للبدء بشكل صحيح.

                    نصائح أساسية:
                    - اختيار النوع المناسب من الألعاب حسب الاهتمام
                    - البدء بالألعاب البسيطة قبل المعقدة
                    - تعلم أساسيات التحكم والواجهات
                    - الانضمام لمجتمعات اللاعبين للتعلم

                    الصبر والممارسة هما مفتاح النجاح في عالم الألعاب.
                    """
                }
            ]

            # اختيار موضوع عشوائي
            selected_topic = random.choice(gaming_topics)

            # إنشاء محتوى أساسي
            auto_article = {
                'title': selected_topic,
                'content': f"""
                هذا مقال تلقائي حول {selected_topic}.

                في عالم الألعاب المتطور باستمرار، نشهد تطورات مثيرة ومستمرة تجعل تجربة اللعب أكثر إثارة ومتعة.

                من أهم النقاط التي يجب مراعاتها:
                - التطور التقني المستمر في صناعة الألعاب
                - تنوع المنصات والخيارات المتاحة للاعبين
                - أهمية المجتمع والتفاعل الاجتماعي في الألعاب
                - التوازن بين الترفيه والتعلم

                هذا المحتوى تم إنشاؤه تلقائياً لضمان استمرارية النشر حتى في حالة عدم توفر مصادر خارجية.
                """,
                'summary': f"مقال تلقائي حول {selected_topic} يغطي أهم النقاط والتطورات في هذا المجال.",
                'source': 'Auto Generated Content',
                'keywords': ['ألعاب', 'تقنية', 'ترفيه', 'تطوير'],
                'timestamp': datetime.now().isoformat(),
                'auto_generated': True
            }

            logger.info(f"✅ تم إنشاء مقال تلقائي: {selected_topic}")
            return auto_article

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحتوى التلقائي: {e}")
            return None

    async def _request_video_approval(self, video_data: Dict, extracted_text: str = None) -> Dict:
        """طلب الموافقة على الفيديو المقترح عبر الواجهة الويب"""
        try:
            if not self.video_approval_system:
                # موافقة تلقائية إذا لم يكن نظام الموافقة متاح
                logger.info("⚠️ نظام الموافقة غير متاح - موافقة تلقائية")
                return {'approved': True, 'reason': 'تلقائي - نظام الموافقة غير متاح'}

            # متغير لحفظ نتيجة الموافقة
            approval_result = {'approved': False, 'reason': 'في انتظار الموافقة'}

            # دالة callback للموافقة
            async def approval_callback(approved: bool, reason: str):
                approval_result['approved'] = approved
                approval_result['reason'] = reason
                logger.info(f"📋 نتيجة الموافقة: {'موافق' if approved else 'مرفوض'} - {reason}")

            # طلب الموافقة عبر النظام الجديد
            approval_id = await self.video_approval_system.request_video_approval(
                video_data, approval_callback, extracted_text
            )

            if approval_id:
                logger.info(f"📋 تم إرسال طلب موافقة للواجهة الويب: {approval_id}")

                # انتظار الموافقة لمدة 5 دقائق كحد أقصى
                timeout = 300  # 5 دقائق
                wait_time = 0

                while wait_time < timeout and not approval_result['approved'] and approval_result['reason'] == 'في انتظار الموافقة':
                    await asyncio.sleep(5)  # فحص كل 5 ثوان
                    wait_time += 5

                if approval_result['approved']:
                    logger.info(f"✅ تمت الموافقة على الفيديو: {video_data.get('title', '')}")
                else:
                    logger.info(f"❌ تم رفض الفيديو أو انتهت المهلة: {video_data.get('title', '')}")
            else:
                # فشل في إرسال طلب الموافقة - موافقة تلقائية
                logger.warning("⚠️ فشل في إرسال طلب الموافقة - موافقة تلقائية")
                approval_result = {'approved': True, 'reason': 'موافقة تلقائية بسبب فشل النظام'}

            # دالة callback للموافقة
            async def approval_callback(approved: bool, reason: str):
                approval_result['approved'] = approved
                approval_result['reason'] = reason

            # إرسال طلب الموافقة مع النص المستخرج (سيوافق تلقائياً ويرسل إشعار)
            await self.video_approval_system.request_video_approval(
                video_data, approval_callback, extracted_text
            )

            # لا حاجة للانتظار - الموافقة تلقائية فورية
            logger.info(f"✅ تم قبول الفيديو تلقائياً: {approval_result['reason']}")
            return approval_result

        except Exception as e:
            logger.error(f"❌ خطأ في طلب الموافقة: {e}")
            return {'approved': True, 'reason': 'موافقة تلقائية - خطأ في النظام'}

    def _generate_title_from_news(self, news_text: str, video_title: str) -> str:
        """توليد عنوان مناسب من النص الإخباري"""
        try:
            # استخراج الكلمات المفتاحية من النص
            words = news_text.split()[:10]  # أول 10 كلمات

            # تنظيف وتحسين العنوان
            title = ' '.join(words)

            # إضافة عناصر جذابة
            if 'أعلن' in news_text or 'announced' in news_text.lower():
                title = f"🚨 عاجل: {title}"
            elif 'جديد' in news_text or 'new' in news_text.lower():
                title = f"🔥 جديد: {title}"
            elif 'تحديث' in news_text or 'update' in news_text.lower():
                title = f"⚡ تحديث: {title}"
            else:
                title = f"📰 {title}"

            # التأكد من طول العنوان - استخدام الحد الأقصى الجديد
            from config.settings import SEOConfig
            if len(title) > SEOConfig.TITLE_LENGTH_MAX:
                # قطع ذكي عند آخر كلمة كاملة
                words = title.split()
                truncated = ""
                for word in words:
                    if len(truncated + word + " ") <= SEOConfig.TITLE_LENGTH_MAX:
                        truncated += word + " "
                    else:
                        break
                title = truncated.strip()  # إزالة النقاط الثلاث

            return title

        except Exception as e:
            logger.error(f"❌ خطأ في توليد العنوان: {e}")
            # إزالة النقاط الثلاث من العنوان الاحتياطي أيضاً
            return f"أخبار من: {video_title[:50]}"

    async def _save_processed_video_data(self, video_data: Dict, transcript: str, analysis_result: Dict):
        """حفظ بيانات الفيديو المعالج في قاعدة البيانات"""
        try:
            channel_info = video_data.get('channel_info', {})

            # بيانات الفيديو للحفظ
            video_record = {
                'video_id': video_data['id'],
                'title': video_data['title'],
                'channel_id': channel_info.get('id', ''),
                'channel_name': channel_info.get('name', ''),
                'duration': video_data.get('duration', 0),
                'published_date': video_data.get('published_at'),
                'transcript_length': len(transcript),
                'news_extracted': len(analysis_result['main_news']),
                'article_id': None  # سيتم تحديثه عند إنشاء المقال
            }

            # حفظ بيانات الفيديو
            video_record_id = db.save_processed_video(video_record)

            # حفظ النص المستخرج
            if video_record_id:
                transcript_data = {
                    'transcript_text': transcript,
                    'language': channel_info.get('language', 'ar'),
                    'main_news_count': len(analysis_result['main_news']),
                    'additional_info_count': len(analysis_result['additional_info'])
                }

                db.save_video_transcript(video_data['id'], transcript_data)

            logger.info(f"💾 تم حفظ بيانات الفيديو المعالج: {video_data['title']}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ بيانات الفيديو: {e}")

    async def _create_trending_content(self) -> List[Dict]:
        """إنشاء محتوى رائج بناءً على الاتجاهات الحالية"""
        try:
            logger.info("🔥 بدء إنشاء محتوى رائج...")

            trending_content = []

            # مواضيع رائجة مبسطة
            trending_topics = [
                {
                    'title': 'أفضل ألعاب 2025 المنتظرة',
                    'content': '''
                    # أفضل ألعاب 2025 المنتظرة

                    يشهد عام 2025 إطلاق العديد من الألعاب المثيرة التي ينتظرها اللاعبون حول العالم.

                    ## أبرز الألعاب المنتظرة:

                    ### 1. Grand Theft Auto VI
                    - تاريخ الإصدار المتوقع: أواخر 2025
                    - المنصات: PlayStation 5, Xbox Series X/S
                    - الميزات الجديدة: عالم مفتوح أكبر، رسوميات محسنة

                    ### 2. The Elder Scrolls VI
                    - لا يزال قيد التطوير
                    - متوقع في نهاية 2025
                    - سيكون حصرياً على Xbox و PC

                    ### 3. Fable
                    - إعادة تشغيل للسلسلة الشهيرة
                    - تطوير Playground Games
                    - عالم خيالي جديد بالكامل

                    ## نصائح للاعبين:
                    - تابع الإعلانات الرسمية
                    - احجز نسختك مبكراً
                    - تأكد من متطلبات النظام

                    هذه الألعاب ستغير مشهد الألعاب في 2025!
                    ''',
                    'summary': 'دليل شامل لأفضل الألعاب المنتظرة في 2025',
                    'keywords': ['gaming 2025', 'upcoming games', 'new releases']
                },
                {
                    'title': 'نصائح الألعاب للمبتدئين 2025',
                    'content': '''
                    # نصائح الألعاب للمبتدئين 2025

                    إذا كنت جديداً في عالم الألعاب، فهذا الدليل سيساعدك على البدء بالطريقة الصحيحة.

                    ## نصائح أساسية:

                    ### 1. اختيار المنصة المناسبة
                    - **PC Gaming**: أفضل للرسوميات والتخصيص
                    - **PlayStation 5**: ألعاب حصرية رائعة
                    - **Xbox Series X**: خدمة Game Pass ممتازة
                    - **Nintendo Switch**: مثالي للألعاب المحمولة

                    ### 2. أنواع الألعاب للمبتدئين
                    - ألعاب المغامرات: سهلة التعلم
                    - ألعاب الألغاز: تطور مهارات التفكير
                    - ألعاب السباق: ممتعة وبسيطة

                    ### 3. إعدادات مهمة
                    - اضبط الصوت والرسوميات
                    - تعلم أزرار التحكم
                    - ابدأ بالمستوى السهل

                    ## أخطاء يجب تجنبها:
                    - شراء ألعاب صعبة جداً في البداية
                    - إهمال التدريب والتعليمات
                    - اللعب لساعات طويلة دون راحة

                    تذكر: الهدف هو الاستمتاع!
                    ''',
                    'summary': 'دليل شامل للمبتدئين في عالم الألعاب',
                    'keywords': ['gaming tips', 'beginner guide', 'gaming advice']
                }
            ]

            for topic in trending_topics:
                try:
                    trending_article = {
                        'title': topic['title'],
                        'content': topic['content'],
                        'summary': topic['summary'],
                        'url': f"generated://trending/{topic['title'].replace(' ', '-')}",
                        'source': 'AI Generated - Trending Content',
                        'source_type': 'trending_generated',
                        'keywords': topic['keywords'],
                        'content_type': 'guide',
                        'trending_score': 95,
                        'publish_date': datetime.now().isoformat()
                    }

                    trending_content.append(trending_article)
                    logger.info(f"✨ تم إنشاء محتوى رائج: {trending_article['title'][:50]}...")

                except Exception as e:
                    logger.warning(f"⚠️ فشل في إنشاء محتوى رائج: {e}")
                    continue

            return trending_content

        except Exception as e:
            logger.error("❌ فشل في إنشاء المحتوى الرائج", e)
            return []

    async def _search_deeper_sources(self) -> List[Dict]:
        """البحث في مصادر أعمق وأقل شهرة"""
        try:
            logger.info("🔍 بدء البحث في المصادر العميقة...")

            deeper_content = []

            # استخدام المصادر الموجودة في النظام
            deeper_sources = [
                "https://arstechnica.com/gaming",
                "https://www.gameinformer.com",
                "https://pcgamesn.com"
            ]

            for source_url in deeper_sources[:2]:  # أفضل 2 مصادر
                try:
                    logger.info(f"🌐 استخراج من المصدر العميق: {source_url}")

                    # استخدام النظام الموجود
                    extracted_articles = await self._collect_from_source(source_url)

                    if extracted_articles:
                        for article in extracted_articles[:2]:  # أفضل 2 مقالات لكل مصدر
                            if len(article.get('content', '')) > 300:
                                article['source_type'] = 'deep_source'
                                article['discovery_method'] = 'deep_search'
                                deeper_content.append(article)

                    # تأخير بين المصادر
                    await asyncio.sleep(3)

                except Exception as e:
                    logger.warning(f"⚠️ فشل في استخراج من المصدر العميق {source_url}: {e}")
                    continue

            # إزالة المحتوى المكرر
            unique_deeper = []
            seen_urls = set()

            for content in deeper_content:
                url = content.get('url', '')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_deeper.append(content)

            logger.info(f"🔍 تم العثور على {len(unique_deeper)} محتوى من المصادر العميقة")
            return unique_deeper[:4]  # أفضل 4 محتويات

        except Exception as e:
            logger.error("❌ فشل في البحث في المصادر العميقة", e)
            return []

    async def _run_performance_monitoring(self):
        """تشغيل مراقبة الأداء المتقدمة"""
        try:
            logger.info("📊 بدء مراقبة الأداء المتقدمة...")

            # 1. تحليل أداء المقالات التلقائي
            logger.info("📈 بدء تحليل أداء المقالات...")
            performance_analysis = await article_performance_analyzer.analyze_all_articles_performance()

            if performance_analysis:
                total_analyzed = performance_analysis.get('total_analyzed', 0)
                overall_analysis = performance_analysis.get('overall_analysis', {})

                logger.info(f"📊 تم تحليل {total_analyzed} مقال")

                if overall_analysis:
                    averages = overall_analysis.get('averages', {})
                    health = overall_analysis.get('overall_health', 'غير محدد')

                    logger.info(f"🎯 الصحة العامة للموقع: {health}")
                    logger.info(f"📈 متوسط CTR: {averages.get('ctr', 0):.2f}%")
                    logger.info(f"⏱️ متوسط وقت القراءة: {averages.get('read_time', 0):.1f} ثانية")
                    logger.info(f"🎪 متوسط نقاط التفاعل: {averages.get('engagement_score', 0):.1f}")

                    # رد أليكس على تحليل الأداء
                    performance_response = ai_personality.generate_personality_response(
                        f"تحليل أداء المقالات أظهر صحة عامة {health} مع متوسط تفاعل {averages.get('engagement_score', 0):.1f}",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {performance_response}")

                    # تحديد المقالات التي تحتاج تحسين
                    poor_performing = overall_analysis.get('poor_performing', [])
                    if poor_performing:
                        logger.info(f"⚠️ {len(poor_performing)} مقال يحتاج تحسين")
                        for article in poor_performing[:3]:  # أسوأ 3 مقالات
                            logger.info(f"   📝 {article.get('title', 'غير محدد')[:50]}... (نقاط: {article.get('score', 0):.1f})")

                        # تشغيل التحسين التلقائي للمحتوى الضعيف
                        logger.info("🔧 بدء التحسين التلقائي للمحتوى الضعيف...")
                        optimization_results = await content_optimizer.run_automatic_optimization()

                        if optimization_results.get('optimized_count', 0) > 0:
                            optimized_count = optimization_results['optimized_count']
                            total_identified = optimization_results.get('total_identified', 0)

                            logger.info(f"✅ تم تحسين {optimized_count} مقال من أصل {total_identified}")

                            # رد أليكس على التحسين
                            optimization_response = ai_personality.generate_personality_response(
                                f"تم تحسين {optimized_count} مقال تلقائياً لتحسين الأداء",
                                'helpful'
                            )
                            logger.info(f"🤖 أليكس: {optimization_response}")

                            # عرض ملخص التحسينات
                            summary = optimization_results.get('optimization_summary', [])
                            for opt in summary[:3]:  # أول 3 تحسينات
                                improvements = ', '.join(opt.get('improvements', []))
                                logger.info(f"   🔧 {opt.get('title', '')[:40]}... → {improvements}")
                        else:
                            logger.info("ℹ️ لا توجد مقالات تحتاج تحسين فوري")

            # 2. تحليل SEO شامل للمقالات الحديثة
            logger.info("🔍 بدء تحليل SEO شامل للمقالات الحديثة...")

            # الحصول على آخر 5 مقالات منشورة
            with sqlite3.connect("data/articles.db") as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title FROM published_articles
                    ORDER BY published_at DESC
                    LIMIT 5
                ''')
                recent_articles = cursor.fetchall()

            if recent_articles:
                seo_results = []

                for article_id, title in recent_articles:
                    try:
                        seo_analysis = await advanced_seo_system.comprehensive_seo_analysis(article_id)

                        if seo_analysis:
                            overall_score = seo_analysis.get('overall_score', 0)
                            seo_results.append({
                                'id': article_id,
                                'title': title,
                                'score': overall_score
                            })

                            logger.info(f"📊 SEO {title[:40]}... → {overall_score:.1f}/100")

                    except Exception as e:
                        logger.error(f"❌ فشل في تحليل SEO للمقال {article_id}", e)
                        continue

                if seo_results:
                    avg_seo_score = sum(r['score'] for r in seo_results) / len(seo_results)
                    logger.info(f"📈 متوسط نقاط SEO: {avg_seo_score:.1f}/100")

                    # رد أليكس على تحليل SEO
                    seo_response = ai_personality.generate_personality_response(
                        f"تحليل SEO أظهر متوسط نقاط {avg_seo_score:.1f} للمقالات الحديثة",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {seo_response}")

                    # عرض المقالات التي تحتاج تحسين SEO
                    poor_seo = [r for r in seo_results if r['score'] < 70]
                    if poor_seo:
                        logger.info(f"⚠️ {len(poor_seo)} مقال يحتاج تحسين SEO:")
                        for article in poor_seo:
                            logger.info(f"   📝 {article['title'][:40]}... (SEO: {article['score']:.1f})")
            else:
                logger.info("ℹ️ لا توجد مقالات حديثة لتحليل SEO")

            # تحديد URL الموقع (يمكن تخصيصه)
            website_url = "https://your-gaming-website.com"  # استبدل بـ URL موقعك الفعلي

            # مراقبة Core Web Vitals
            async with api_manager:
                core_vitals_analyzer = CoreWebVitalsAnalyzer(api_manager)
                vitals_report = await core_vitals_analyzer.analyze_page_performance(website_url)

                if vitals_report:
                    overall_score = vitals_report.get('overall_score', 0)
                    logger.info(f"🎯 Core Web Vitals Score: {overall_score}/100")

                    # تحليل النتائج مع أليكس
                    vitals_analysis = ai_personality.generate_personality_response(
                        f"تحليل Core Web Vitals أظهر نقاط {overall_score}/100",
                        'analytical'
                    )
                    logger.info(f"🤖 تحليل أليكس: {vitals_analysis}")

                # بحث الكلمات المفتاحية المتقدم
                keyword_research = KeywordResearchAPI(api_manager)
                gaming_keywords = ['gaming news', 'video game reviews', 'game updates', 'esports news', 'new game releases', 'gaming industry']

                keyword_opportunities = await keyword_research.comprehensive_keyword_research(gaming_keywords)

                if keyword_opportunities:
                    total_opportunities = len(keyword_opportunities.get('keyword_opportunities', []))
                    logger.info(f"🔍 تم اكتشاف {total_opportunities} فرصة كلمات مفتاحية جديدة")

                    # قرار أليكس حول الكلمات المفتاحية
                    keyword_decision = ai_personality.make_personality_driven_decision(
                        context={
                            'situation': 'keyword_optimization',
                            'urgency': 'medium',
                            'impact': 'high',
                            'data': {'opportunities_found': total_opportunities}
                        },
                        options=[
                            {
                                'name': 'focus_high_volume_keywords',
                                'data_support': 8,
                                'long_term_impact': 9,
                                'risk_level': 3,
                                'user_benefit': 8
                            },
                            {
                                'name': 'target_long_tail_keywords',
                                'data_support': 7,
                                'long_term_impact': 7,
                                'risk_level': 2,
                                'user_benefit': 7
                            }
                        ]
                    )

                    chosen_strategy = keyword_decision.get('chosen_option', {}).get('option', {}).get('name', 'balanced_approach')
                    logger.info(f"🎯 استراتيجية أليكس للكلمات المفتاحية: {chosen_strategy}")

                # تحليل Microsoft Clarity
                clarity_insights = await clarity_analyzer.get_clarity_insights(days=7)
                if clarity_insights:
                    engagement_score = clarity_insights.get('analysis', {}).get('user_engagement', {}).get('engagement_score', 0)
                    logger.info(f"📊 نقاط تفاعل المستخدمين (Clarity): {engagement_score}/100")

                # بحث Ubersuggest للكلمات المفتاحية
                ubersuggest_results = await ubersuggest_api.get_keyword_suggestions('gaming news', 'ar', 'SA')
                if ubersuggest_results:
                    total_suggestions = ubersuggest_results.get('analysis', {}).get('total_suggestions', 0)
                    logger.info(f"🔍 اقتراحات Ubersuggest: {total_suggestions} كلمة مفتاحية جديدة")

        except Exception as e:
            logger.error("❌ فشل في مراقبة الأداء المتقدمة", e)

    async def _collect_from_source(self, source_url: str) -> List[Dict]:
        """جمع المحتوى من مصدر واحد"""
        try:
            logger.info(f"🌐 استخراج المحتوى من: {source_url}")
            articles = self.scraper.extract_articles(source_url, "deep_source")

            if articles:
                logger.info(f"✅ تم استخراج {len(articles)} مقال من {source_url}")
                return articles
            else:
                logger.info(f"📭 لم يتم العثور على مقالات في {source_url}")
                return []

        except Exception as e:
            logger.warning(f"⚠️ فشل في استخراج المحتوى من {source_url}: {e}")
            return []

    def _log_failed_sources_report(self):
        """تسجيل تقرير عن المصادر المعطلة"""
        try:
            failed_sources = self.scraper.get_failed_sources()
            if failed_sources:
                logger.warning(f"📊 تقرير المصادر المعطلة: {len(failed_sources)} مصدر معطل")
                for source in list(failed_sources)[:5]:  # عرض أول 5 مصادر فقط
                    logger.warning(f"🚫 مصدر معطل: {source}")
                if len(failed_sources) > 5:
                    logger.warning(f"... و {len(failed_sources) - 5} مصدر آخر معطل")
            else:
                logger.info("✅ جميع المصادر تعمل بشكل طبيعي")
        except Exception as e:
            logger.error("❌ فشل في إنشاء تقرير المصادر المعطلة", e)

def setup_environment() -> bool:
    """إعداد البيئة والمتطلبات"""
    try:
        # إنشاء المجلدات المطلوبة
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('config', exist_ok=True)
        os.makedirs('images', exist_ok=True)
        os.makedirs('reports', exist_ok=True)

        return True

    except Exception as e:
        print(f"❌ فشل في إعداد البيئة: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    
    # إعداد البيئة
    if not setup_environment():
        sys.exit(1)
    
    # إنشاء البوت
    bot = GamingNewsBot()
    
    try:
        # تشغيل البوت
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("⌨️ تم طلب الإيقاف بواسطة المستخدم")
    except Exception as e:
        logger.critical("❌ خطأ غير متوقع", e)
        sys.exit(1)

if __name__ == "__main__":
    try:
        # رسالة ترحيب
        print("=" * 60)
        print("🎮 وكيل أخبار الألعاب - الإصدار الجديد")
        print("=" * 60)
        print("🌐 واجهة الويب: http://localhost:5000")
        print("🚀 سيتم فتح المتصفح تلقائياً...")
        print("=" * 60)

        # تشغيل البوت
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⌨️ تم الإيقاف بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        sys.exit(1)
