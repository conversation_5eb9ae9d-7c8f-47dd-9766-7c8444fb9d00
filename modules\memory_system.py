# نظام الذاكرة المتقدم للوكيل الذكي
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

try:
    import pinecone
    from sentence_transformers import SentenceTransformer
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False
    print("⚠️ Pinecone غير متوفر. سيتم استخدام الذاكرة المحلية.")

from .logger import logger
from config.settings import BotConfig

class MemoryType(Enum):
    """أنواع الذكريات"""
    ARTICLE = "article"           # ذكريات المقالات
    USER_INTERACTION = "user_interaction"  # تفاعلات المستخدمين
    SEARCH_QUERY = "search_query"  # استعلامات البحث
    PERFORMANCE = "performance"    # أداء المحتوى
    TREND = "trend"               # الاتجاهات
    PREFERENCE = "preference"     # التفضيلات

class MemoryImportance(Enum):
    """مستويات أهمية الذكريات"""
    CRITICAL = 5    # حرج - لا يُحذف أبداً
    HIGH = 4        # عالي - يُحذف بعد سنة
    MEDIUM = 3      # متوسط - يُحذف بعد 6 أشهر
    LOW = 2         # منخفض - يُحذف بعد 3 أشهر
    TEMPORARY = 1   # مؤقت - يُحذف بعد شهر

@dataclass
class Memory:
    """كلاس الذاكرة الأساسي"""
    id: str
    content: str
    memory_type: MemoryType
    importance: MemoryImportance
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    created_at: datetime = None
    last_accessed: datetime = None
    access_count: int = 0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.last_accessed is None:
            self.last_accessed = datetime.now()
        if self.tags is None:
            self.tags = []

class AdvancedMemorySystem:
    """نظام الذاكرة المتقدم للوكيل"""
    
    def __init__(self):
        self.embedding_model = None
        self.pinecone_index = None
        self.local_memories = {}  # احتياطي محلي
        self.memory_stats = {
            'total_memories': 0,
            'memories_by_type': {},
            'memories_by_importance': {},
            'last_cleanup': datetime.now(),
            'retrieval_stats': {}
        }
        
        # تهيئة النظام
        self._initialize_system()
    
    def _initialize_system(self):
        """تهيئة نظام الذاكرة"""
        try:
            # تحميل نموذج التضمين
            logger.info("🧠 تحميل نموذج التضمين...")
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("✅ تم تحميل نموذج التضمين")
            
            # تهيئة Pinecone إذا كان متوفراً
            if PINECONE_AVAILABLE and hasattr(BotConfig, 'PINECONE_API_KEY'):
                self._initialize_pinecone()
            else:
                logger.warning("⚠️ سيتم استخدام الذاكرة المحلية فقط")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة نظام الذاكرة: {e}")
    
    def _initialize_pinecone(self):
        """تهيئة Pinecone"""
        try:
            pinecone.init(
                api_key=BotConfig.PINECONE_API_KEY,
                environment=getattr(BotConfig, 'PINECONE_ENVIRONMENT', 'us-west1-gcp')
            )
            
            index_name = "gaming-agent-memory"
            
            # إنشاء الفهرس إذا لم يكن موجوداً
            if index_name not in pinecone.list_indexes():
                pinecone.create_index(
                    name=index_name,
                    dimension=384,  # بُعد نموذج all-MiniLM-L6-v2
                    metric="cosine"
                )
                logger.info(f"✅ تم إنشاء فهرس Pinecone: {index_name}")
            
            self.pinecone_index = pinecone.Index(index_name)
            logger.info("✅ تم الاتصال بـ Pinecone بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Pinecone: {e}")
            self.pinecone_index = None
    
    def _generate_memory_id(self, content: str, memory_type: MemoryType) -> str:
        """توليد معرف فريد للذاكرة"""
        timestamp = str(int(time.time()))
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        return f"{memory_type.value}_{timestamp}_{content_hash}"
    
    def _create_embedding(self, text: str) -> List[float]:
        """إنشاء تضمين للنص"""
        try:
            if self.embedding_model:
                embedding = self.embedding_model.encode(text)
                return embedding.tolist()
            return []
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء التضمين: {e}")
            return []
    
    async def store_memory(self, 
                          content: str, 
                          memory_type: MemoryType,
                          importance: MemoryImportance,
                          metadata: Dict[str, Any] = None,
                          tags: List[str] = None) -> str:
        """حفظ ذاكرة جديدة"""
        try:
            # إنشاء معرف الذاكرة
            memory_id = self._generate_memory_id(content, memory_type)
            
            # إنشاء التضمين
            embedding = self._create_embedding(content)
            
            # إنشاء كائن الذاكرة
            memory = Memory(
                id=memory_id,
                content=content,
                memory_type=memory_type,
                importance=importance,
                metadata=metadata or {},
                embedding=embedding,
                tags=tags or []
            )
            
            # حفظ في Pinecone إذا كان متوفراً
            if self.pinecone_index and embedding:
                vector_data = {
                    'id': memory_id,
                    'values': embedding,
                    'metadata': {
                        'content': content,
                        'type': memory_type.value,
                        'importance': importance.value,
                        'created_at': memory.created_at.isoformat(),
                        'tags': tags or [],
                        **metadata or {}
                    }
                }
                self.pinecone_index.upsert([vector_data])
            
            # حفظ محلياً كاحتياط
            self.local_memories[memory_id] = memory
            
            # تحديث الإحصائيات
            self._update_stats(memory_type, importance)
            
            logger.info(f"💾 تم حفظ ذاكرة: {memory_type.value} - {memory_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الذاكرة: {e}")
            return None
    
    async def retrieve_memories(self, 
                               query: str, 
                               memory_types: List[MemoryType] = None,
                               limit: int = 10,
                               min_importance: MemoryImportance = MemoryImportance.LOW) -> List[Memory]:
        """استرجاع الذكريات ذات الصلة"""
        try:
            # إنشاء تضمين للاستعلام
            query_embedding = self._create_embedding(query)
            
            memories = []
            
            # البحث في Pinecone إذا كان متوفراً
            if self.pinecone_index and query_embedding:
                # إنشاء فلتر للبحث
                filter_dict = {}
                if memory_types:
                    filter_dict['type'] = {'$in': [t.value for t in memory_types]}
                if min_importance:
                    filter_dict['importance'] = {'$gte': min_importance.value}
                
                # البحث
                results = self.pinecone_index.query(
                    vector=query_embedding,
                    top_k=limit,
                    filter=filter_dict if filter_dict else None,
                    include_metadata=True
                )
                
                # تحويل النتائج إلى كائنات Memory
                for match in results['matches']:
                    metadata = match['metadata']
                    memory = Memory(
                        id=match['id'],
                        content=metadata.get('content', ''),
                        memory_type=MemoryType(metadata.get('type', 'article')),
                        importance=MemoryImportance(metadata.get('importance', 2)),
                        metadata=metadata,
                        created_at=datetime.fromisoformat(metadata.get('created_at', datetime.now().isoformat())),
                        tags=metadata.get('tags', [])
                    )
                    memories.append(memory)
            
            # البحث المحلي كاحتياط
            if not memories:
                memories = self._search_local_memories(query, memory_types, limit, min_importance)
            
            # تحديث إحصائيات الوصول
            for memory in memories:
                self._update_access_stats(memory.id)
            
            logger.info(f"🔍 تم استرجاع {len(memories)} ذاكرة للاستعلام: {query}")
            return memories
            
        except Exception as e:
            logger.error(f"❌ خطأ في استرجاع الذكريات: {e}")
            return []
    
    def _search_local_memories(self, 
                              query: str, 
                              memory_types: List[MemoryType],
                              limit: int,
                              min_importance: MemoryImportance) -> List[Memory]:
        """البحث في الذكريات المحلية"""
        matching_memories = []
        query_lower = query.lower()
        
        for memory in self.local_memories.values():
            # فلترة حسب النوع
            if memory_types and memory.memory_type not in memory_types:
                continue
            
            # فلترة حسب الأهمية
            if memory.importance.value < min_importance.value:
                continue
            
            # البحث في المحتوى
            if query_lower in memory.content.lower():
                matching_memories.append(memory)
        
        # ترتيب حسب الأهمية والوقت
        matching_memories.sort(
            key=lambda m: (m.importance.value, m.created_at),
            reverse=True
        )
        
        return matching_memories[:limit]
    
    def _update_stats(self, memory_type: MemoryType, importance: MemoryImportance):
        """تحديث إحصائيات الذاكرة"""
        self.memory_stats['total_memories'] += 1
        
        # إحصائيات حسب النوع
        type_key = memory_type.value
        if type_key not in self.memory_stats['memories_by_type']:
            self.memory_stats['memories_by_type'][type_key] = 0
        self.memory_stats['memories_by_type'][type_key] += 1
        
        # إحصائيات حسب الأهمية
        importance_key = importance.name
        if importance_key not in self.memory_stats['memories_by_importance']:
            self.memory_stats['memories_by_importance'][importance_key] = 0
        self.memory_stats['memories_by_importance'][importance_key] += 1
    
    def _update_access_stats(self, memory_id: str):
        """تحديث إحصائيات الوصول"""
        if memory_id in self.local_memories:
            memory = self.local_memories[memory_id]
            memory.last_accessed = datetime.now()
            memory.access_count += 1
        
        # تحديث إحصائيات الاسترجاع
        if memory_id not in self.memory_stats['retrieval_stats']:
            self.memory_stats['retrieval_stats'][memory_id] = 0
        self.memory_stats['retrieval_stats'][memory_id] += 1
    
    async def cleanup_old_memories(self):
        """تنظيف الذكريات القديمة"""
        try:
            logger.info("🧹 بدء تنظيف الذكريات القديمة...")
            
            current_time = datetime.now()
            memories_to_delete = []
            
            # تحديد الذكريات المراد حذفها
            for memory_id, memory in self.local_memories.items():
                age = current_time - memory.created_at
                
                # تحديد مدة الاحتفاظ حسب الأهمية
                retention_days = {
                    MemoryImportance.CRITICAL: float('inf'),  # لا يُحذف أبداً
                    MemoryImportance.HIGH: 365,               # سنة
                    MemoryImportance.MEDIUM: 180,             # 6 أشهر
                    MemoryImportance.LOW: 90,                 # 3 أشهر
                    MemoryImportance.TEMPORARY: 30            # شهر
                }
                
                max_age = retention_days.get(memory.importance, 90)
                if age.days > max_age:
                    memories_to_delete.append(memory_id)
            
            # حذف الذكريات القديمة
            for memory_id in memories_to_delete:
                await self._delete_memory(memory_id)
            
            self.memory_stats['last_cleanup'] = current_time
            logger.info(f"✅ تم حذف {len(memories_to_delete)} ذاكرة قديمة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الذكريات: {e}")
    
    async def _delete_memory(self, memory_id: str):
        """حذف ذاكرة محددة"""
        try:
            # حذف من Pinecone
            if self.pinecone_index:
                self.pinecone_index.delete(ids=[memory_id])
            
            # حذف من الذاكرة المحلية
            if memory_id in self.local_memories:
                del self.local_memories[memory_id]
            
            logger.debug(f"🗑️ تم حذف الذاكرة: {memory_id}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الذاكرة {memory_id}: {e}")
    
    def get_memory_stats(self) -> Dict:
        """الحصول على إحصائيات الذاكرة"""
        return {
            **self.memory_stats,
            'local_memories_count': len(self.local_memories),
            'pinecone_available': self.pinecone_index is not None,
            'embedding_model_loaded': self.embedding_model is not None
        }

# إنشاء مثيل عام
memory_system = AdvancedMemorySystem()
